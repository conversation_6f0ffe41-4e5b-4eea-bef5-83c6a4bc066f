/**
 * 简化的Mock系统初始化脚本
 * 
 * 直接在浏览器环境中初始化Mock系统，避免模块导入问题
 */

// Mock系统配置
const MOCK_CONFIG = {
  enabled: process.env.VUE_APP_ENABLE_MOCK === 'true',
  serviceWorker: {
    scriptPath: '/sw-mock-manager.js',
    debug: true
  },
  runtime: {
    allowToggle: true,
    consolePrefix: 'MockSystem',
    storageKey: 'mock-system-state'
  },
  apis: {
    '/st/lifePay/providerInfo/*': true,
    '/st/lifePay/bankFlow/*': true,
    '/st/lifePay/providerInfo/queryPage': true,
    '/st/lifePay/providerInfo/getDropLists': true,
    '/st/lifePay/providerInfo/add': true,
    '/st/lifePay/providerInfo/edit': true,
    '/st/lifePay/providerInfo/delete': true,
    '/st/lifePay/providerInfo/exportExcel': true,
    '/st/lifePay/providerInfo/importExcel': true
  }
};

// Mock工具函数
const MockUtils = {
  isApiEnabled: (url) => {
    if (!MOCK_CONFIG.enabled) return false;
    
    // 检查精确匹配
    if (MOCK_CONFIG.apis.hasOwnProperty(url)) {
      return MOCK_CONFIG.apis[url];
    }
    
    // 检查通配符匹配
    for (const pattern in MOCK_CONFIG.apis) {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        if (regex.test(url) && MOCK_CONFIG.apis[pattern]) {
          return true;
        }
      }
    }
    
    return false;
  }
};

// Mock系统类
class SimpleMockSystem {
  constructor() {
    this.isInitialized = false;
    this.serviceWorker = null;
    this.config = MOCK_CONFIG;
  }

  async init() {
    if (this.isInitialized) {
      console.log('[MockSystem] Already initialized');
      return true;
    }

    if (!this.config.enabled) {
      console.log('[MockSystem] Mock system is disabled');
      return false;
    }

    if (!('serviceWorker' in navigator)) {
      console.warn('[MockSystem] ServiceWorker not supported');
      return false;
    }

    try {
      // 注册ServiceWorker
      const registration = await navigator.serviceWorker.register(
        this.config.serviceWorker.scriptPath,
        { scope: '/' }
      );

      console.log('[MockSystem] ServiceWorker registered:', registration);

      // 等待ServiceWorker激活
      await this.waitForServiceWorker(registration);

      this.isInitialized = true;
      console.log('[MockSystem] Mock system initialized successfully');

      // 设置全局控制
      this.setupGlobalControls();

      return true;

    } catch (error) {
      console.error('[MockSystem] Failed to initialize:', error);
      return false;
    }
  }

  waitForServiceWorker(registration) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('ServiceWorker activation timeout'));
      }, 10000);

      if (registration.active) {
        clearTimeout(timeout);
        this.serviceWorker = registration.active;
        resolve(registration.active);
        return;
      }

      const worker = registration.installing || registration.waiting;
      if (worker) {
        worker.addEventListener('statechange', () => {
          if (worker.state === 'activated') {
            clearTimeout(timeout);
            this.serviceWorker = worker;
            resolve(worker);
          }
        });
      } else {
        clearTimeout(timeout);
        reject(new Error('No ServiceWorker found'));
      }
    });
  }

  setupGlobalControls() {
    window.MockSystem = {
      enable: () => {
        this.config.enabled = true;
        console.log('[MockSystem] Mock system enabled');
      },
      disable: () => {
        this.config.enabled = false;
        console.log('[MockSystem] Mock system disabled');
      },
      clearCache: () => {
        if (this.serviceWorker) {
          this.serviceWorker.postMessage({ type: 'CLEAR_CACHE' });
        }
        console.log('[MockSystem] Cache cleared');
      },
      status: () => {
        const status = {
          enabled: this.config.enabled,
          initialized: this.isInitialized,
          serviceWorker: !!this.serviceWorker
        };
        console.log('Mock System Status:', status);
        return status;
      },
      config: () => {
        console.log('Mock System Config:', this.config);
        return this.config;
      },
      apis: () => {
        console.log('Mocked APIs:', this.config.apis);
        return this.config.apis;
      },
      test: (url) => {
        const isMocked = MockUtils.isApiEnabled(url);
        console.log(`API ${url} is ${isMocked ? 'MOCKED' : 'NOT MOCKED'}`);
        return isMocked;
      }
    };

    console.log('[MockSystem] Global controls available via window.MockSystem');
  }

  getStatus() {
    return {
      enabled: this.config.enabled,
      initialized: this.isInitialized,
      serviceWorker: !!this.serviceWorker,
      config: this.config
    };
  }
}

// 创建全局实例并自动初始化
const mockSystem = new SimpleMockSystem();

// 自动初始化
function initMockSystem() {
  if (MOCK_CONFIG.enabled && typeof window !== 'undefined') {
    console.log('[MockInit] Starting Mock System initialization...');
    
    mockSystem.init().then(success => {
      if (success) {
        console.log('[MockInit] Mock System ready!');
      } else {
        console.warn('[MockInit] Mock System initialization failed');
      }
    }).catch(error => {
      console.error('[MockInit] Mock System error:', error);
    });
  } else {
    console.log('[MockInit] Mock System disabled or not in browser environment');
  }
}

// 导出初始化函数
export default initMockSystem;
export { mockSystem, MOCK_CONFIG, MockUtils };
