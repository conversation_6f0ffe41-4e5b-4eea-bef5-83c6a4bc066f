/**
 * Mock 系统初始化和管理工具
 * 
 * 负责初始化ServiceWorker、配置传递、运行时控制等功能
 * 与现有的Axios封装无缝集成
 */

import { MOCK_CONFIG, MockUtils } from '../../mock.config.js';

class MockSystem {
  constructor() {
    this.isInitialized = false;
    this.serviceWorker = null;
    this.config = MOCK_CONFIG;
    this.utils = MockUtils;
  }

  /**
   * 初始化Mock系统
   * @returns {Promise<boolean>}
   */
  async init() {
    if (this.isInitialized) {
      console.log('[MockSystem] Already initialized');
      return true;
    }

    // 检查是否启用Mock系统
    if (!this.config.enabled) {
      console.log('[MockSystem] Mock system is disabled');
      return false;
    }

    // 检查浏览器是否支持ServiceWorker
    if (!('serviceWorker' in navigator)) {
      console.warn('[MockSystem] ServiceWorker not supported');
      return false;
    }

    try {
      // 注册ServiceWorker
      const registration = await navigator.serviceWorker.register(
        this.config.serviceWorker.scriptPath,
        { scope: '/' }
      );

      console.log('[MockSystem] ServiceWorker registered:', registration);

      // 等待ServiceWorker激活
      await this.waitForServiceWorker(registration);

      // 发送配置到ServiceWorker
      await this.sendConfigToServiceWorker();

      this.isInitialized = true;
      console.log('[MockSystem] Mock system initialized successfully');

      // 设置运行时控制
      this.setupRuntimeControls();

      return true;

    } catch (error) {
      console.error('[MockSystem] Failed to initialize:', error);
      return false;
    }
  }

  /**
   * 等待ServiceWorker激活
   * @param {ServiceWorkerRegistration} registration
   * @returns {Promise<ServiceWorker>}
   */
  waitForServiceWorker(registration) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('ServiceWorker activation timeout'));
      }, 10000);

      if (registration.active) {
        clearTimeout(timeout);
        this.serviceWorker = registration.active;
        resolve(registration.active);
        return;
      }

      const worker = registration.installing || registration.waiting;
      if (worker) {
        worker.addEventListener('statechange', () => {
          if (worker.state === 'activated') {
            clearTimeout(timeout);
            this.serviceWorker = worker;
            resolve(worker);
          }
        });
      } else {
        clearTimeout(timeout);
        reject(new Error('No ServiceWorker found'));
      }
    });
  }

  /**
   * 发送配置到ServiceWorker
   * @returns {Promise<void>}
   */
  async sendConfigToServiceWorker() {
    if (!this.serviceWorker) {
      throw new Error('ServiceWorker not available');
    }

    // 发送初始化配置
    this.serviceWorker.postMessage({
      type: 'INIT_MOCK_CONFIG',
      data: {
        config: this.config,
        utils: {
          // 只传递可序列化的工具函数
          isApiEnabled: this.utils.isApiEnabled.toString(),
          isModuleEnabled: this.utils.isModuleEnabled.toString()
        }
      }
    });

    console.log('[MockSystem] Configuration sent to ServiceWorker');
  }

  /**
   * 更新Mock配置
   * @param {object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    if (this.serviceWorker) {
      this.serviceWorker.postMessage({
        type: 'UPDATE_MOCK_CONFIG',
        data: newConfig
      });
    }

    console.log('[MockSystem] Configuration updated');
  }

  /**
   * 启用Mock系统
   */
  enable() {
    this.updateConfig({ enabled: true });
    
    if (this.config.runtime.allowToggle) {
      localStorage.setItem(this.config.runtime.storageKey, 'true');
    }
    
    console.log('[MockSystem] Mock system enabled');
  }

  /**
   * 禁用Mock系统
   */
  disable() {
    this.updateConfig({ enabled: false });
    
    if (this.config.runtime.allowToggle) {
      localStorage.setItem(this.config.runtime.storageKey, 'false');
    }
    
    console.log('[MockSystem] Mock system disabled');
  }

  /**
   * 清除Mock数据缓存
   */
  clearCache() {
    if (this.serviceWorker) {
      this.serviceWorker.postMessage({
        type: 'CLEAR_CACHE'
      });
    }
    
    console.log('[MockSystem] Mock data cache cleared');
  }

  /**
   * 检查接口是否启用模拟
   * @param {string} url - 接口URL
   * @returns {boolean}
   */
  isApiMocked(url) {
    return this.utils.isApiEnabled(url);
  }

  /**
   * 获取Mock数据文件路径
   * @param {string} module - 模块名
   * @param {string} action - 操作名
   * @returns {string}
   */
  getMockDataPath(module, action) {
    return this.utils.getMockDataPath(module, action);
  }

  /**
   * 设置运行时控制
   */
  setupRuntimeControls() {
    if (!this.config.runtime.allowToggle) {
      return;
    }

    // 添加全局控制对象
    const prefix = this.config.runtime.consolePrefix;
    window[prefix] = {
      enable: () => this.enable(),
      disable: () => this.disable(),
      clearCache: () => this.clearCache(),
      status: () => {
        console.log('Mock System Status:', {
          enabled: this.config.enabled,
          initialized: this.isInitialized,
          serviceWorker: !!this.serviceWorker
        });
      },
      config: () => {
        console.log('Mock System Config:', this.config);
      },
      apis: () => {
        console.log('Mocked APIs:', this.config.apis);
      },
      test: (url) => {
        const isMocked = this.isApiMocked(url);
        console.log(`API ${url} is ${isMocked ? 'MOCKED' : 'NOT MOCKED'}`);
        return isMocked;
      }
    };

    console.log(`[MockSystem] Runtime controls available via window.${prefix}`);
  }

  /**
   * 获取系统状态
   * @returns {object}
   */
  getStatus() {
    return {
      enabled: this.config.enabled,
      initialized: this.isInitialized,
      serviceWorker: !!this.serviceWorker,
      config: this.config
    };
  }

  /**
   * 销毁Mock系统
   */
  async destroy() {
    if (this.serviceWorker) {
      try {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
          await registration.unregister();
          console.log('[MockSystem] ServiceWorker unregistered');
        }
      } catch (error) {
        console.error('[MockSystem] Failed to unregister ServiceWorker:', error);
      }
    }

    this.isInitialized = false;
    this.serviceWorker = null;
    
    // 清理全局控制对象
    if (this.config.runtime.allowToggle) {
      delete window[this.config.runtime.consolePrefix];
    }

    console.log('[MockSystem] Mock system destroyed');
  }
}

// 创建全局实例
const mockSystem = new MockSystem();

// 自动初始化（如果启用）
if (MOCK_CONFIG.enabled && typeof window !== 'undefined') {
  // 在DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      mockSystem.init().catch(console.error);
    });
  } else {
    mockSystem.init().catch(console.error);
  }
}

export default mockSystem;
export { MockSystem, MOCK_CONFIG, MockUtils };
