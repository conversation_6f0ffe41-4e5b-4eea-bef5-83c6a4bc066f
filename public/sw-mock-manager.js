/**
 * ServiceWorker Mock Manager
 *
 * 核心ServiceWorker模拟请求处理模块，负责拦截API请求并返回模拟数据
 * 支持与现有Axios封装的无缝集成，保持相同的响应格式和状态码
 */

// Mock配置（从主线程传递过来）
let mockConfig = null;
let mockUtils = null;

// 缓存的Mock数据
const mockDataCache = new Map();

// 日志工具
const Logger = {
  log: (message, ...args) => {
    if (mockConfig?.serviceWorker?.debug) {
      console.log(`[SW-Mock] ${message}`, ...args);
    }
  },
  error: (message, ...args) => {
    console.error(`[SW-Mock] ${message}`, ...args);
  },
  warn: (message, ...args) => {
    console.warn(`[SW-Mock] ${message}`, ...args);
  },
};

// ServiceWorker 安装事件
self.addEventListener("install", (event) => {
  Logger.log("ServiceWorker installing...");
  self.skipWaiting();
});

// ServiceWorker 激活事件
self.addEventListener("activate", (event) => {
  Logger.log("ServiceWorker activated");
  event.waitUntil(self.clients.claim());
});

// 监听来自主线程的消息
self.addEventListener("message", (event) => {
  const { type, data } = event.data;

  switch (type) {
    case "INIT_MOCK_CONFIG":
      mockConfig = data.config;
      mockUtils = data.utils;
      Logger.log("Mock config initialized", mockConfig);
      break;
    case "UPDATE_MOCK_CONFIG":
      mockConfig = { ...mockConfig, ...data };
      Logger.log("Mock config updated");
      break;
    case "CLEAR_CACHE":
      mockDataCache.clear();
      Logger.log("Mock data cache cleared");
      break;
  }
});

// 主要的请求拦截处理
self.addEventListener("fetch", (event) => {
  const request = event.request;
  const url = new URL(request.url);

  // 只处理API请求
  if (!isApiRequest(url)) {
    return;
  }

  // 提取API路径，去掉base path前缀
  let apiPath = url.pathname;

  // 如果路径包含 /charging-maintenance-server，则去掉这个前缀
  if (apiPath.includes("/charging-maintenance-server")) {
    apiPath = apiPath.replace("/charging-maintenance-server", "");
  }

  // 检查是否需要模拟此接口
  if (!shouldMockApi(apiPath)) {
    Logger.log(`API not mocked: ${apiPath} (original: ${url.pathname})`);
    return;
  }

  Logger.log(
    `Intercepting API: ${request.method} ${apiPath} (original: ${url.pathname})`
  );

  // 拦截请求并返回模拟响应
  event.respondWith(handleMockRequest(request, apiPath));
});

/**
 * 判断是否为API请求
 * @param {URL} url - 请求URL对象
 * @returns {boolean}
 */
function isApiRequest(url) {
  // 检查是否为API路径
  const apiPaths = [
    "/api/",
    "/st/",
    "/system/",
    "/charging-maintenance-server/",
  ];
  return apiPaths.some((path) => url.pathname.includes(path));
}

/**
 * 判断是否应该模拟此API
 * @param {string} apiPath - API路径
 * @returns {boolean}
 */
function shouldMockApi(apiPath) {
  if (!mockConfig?.enabled) {
    return false;
  }

  // 检查运行时状态
  if (mockConfig.runtime?.allowToggle) {
    const runtimeState = self.localStorage?.getItem(
      mockConfig.runtime.storageKey
    );
    if (runtimeState === "false") {
      return false;
    }
  }

  // 使用配置中的检查逻辑
  return checkApiEnabled(apiPath);
}

/**
 * 检查API是否启用（模拟mockUtils.isApiEnabled）
 * @param {string} url - API URL
 * @returns {boolean}
 */
function checkApiEnabled(url) {
  if (!mockConfig?.apis) return false;

  // 检查精确匹配
  if (mockConfig.apis.hasOwnProperty(url)) {
    return mockConfig.apis[url];
  }

  // 检查通配符匹配
  for (const pattern in mockConfig.apis) {
    if (pattern.includes("*")) {
      const regex = new RegExp(pattern.replace(/\*/g, ".*"));
      if (regex.test(url) && mockConfig.apis[pattern]) {
        return true;
      }
    }
  }

  return false;
}

/**
 * 处理模拟请求
 * @param {Request} request - 原始请求
 * @param {string} apiPath - API路径
 * @returns {Promise<Response>}
 */
async function handleMockRequest(request, apiPath) {
  try {
    // 解析请求参数
    const requestData = await parseRequestData(request);
    Logger.log(`Request data:`, requestData);

    // 模拟网络延迟
    await simulateNetworkDelay();

    // 获取模拟数据
    const mockData = await getMockData(apiPath, request.method, requestData);

    // 处理特殊接口类型
    if (isDownloadApi(apiPath)) {
      return handleDownloadResponse(mockData);
    }

    if (isUploadApi(apiPath)) {
      return handleUploadResponse(mockData);
    }

    // 生成标准响应
    const response = generateMockResponse(mockData, apiPath, requestData);

    Logger.log(`Mock response for ${apiPath}:`, response);

    return new Response(JSON.stringify(response), {
      status: 200,
      statusText: "OK",
      headers: {
        "Content-Type": "application/json;charset=utf-8",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  } catch (error) {
    Logger.error(`Error handling mock request for ${apiPath}:`, error);

    // 返回错误响应
    const errorResponse = generateErrorResponse(error.message);
    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      statusText: "Internal Server Error",
      headers: {
        "Content-Type": "application/json;charset=utf-8",
      },
    });
  }
}

/**
 * 解析请求数据
 * @param {Request} request - 请求对象
 * @returns {Promise<object>}
 */
async function parseRequestData(request) {
  const contentType = request.headers.get("content-type") || "";

  if (contentType.includes("application/json")) {
    try {
      return await request.json();
    } catch (e) {
      return {};
    }
  }

  if (contentType.includes("multipart/form-data")) {
    try {
      const formData = await request.formData();
      const data = {};
      for (const [key, value] of formData.entries()) {
        data[key] = value;
      }
      return data;
    } catch (e) {
      return {};
    }
  }

  // GET请求参数
  const url = new URL(request.url);
  const params = {};
  for (const [key, value] of url.searchParams.entries()) {
    params[key] = value;
  }

  return params;
}

/**
 * 模拟网络延迟
 * @returns {Promise<void>}
 */
function simulateNetworkDelay() {
  if (!mockConfig?.serviceWorker?.networkDelay) {
    return Promise.resolve();
  }

  const { min, max } = mockConfig.serviceWorker.networkDelay;
  const delay = Math.random() * (max - min) + min;

  return new Promise((resolve) => setTimeout(resolve, delay));
}

/**
 * 获取模拟数据
 * @param {string} apiPath - API路径
 * @param {string} method - HTTP方法
 * @param {object} requestData - 请求数据
 * @returns {Promise<any>}
 */
async function getMockData(apiPath, method, requestData) {
  // 生成缓存键
  const cacheKey = `${method}:${apiPath}`;

  // 检查缓存
  if (mockDataCache.has(cacheKey)) {
    Logger.log(`Using cached data for ${cacheKey}`);
    return mockDataCache.get(cacheKey);
  }

  // 根据API路径确定模拟数据文件
  const mockDataPath = determineMockDataPath(apiPath);

  try {
    // 加载模拟数据文件
    const response = await fetch(mockDataPath);
    if (!response.ok) {
      throw new Error(`Failed to load mock data: ${mockDataPath}`);
    }

    const mockData = await response.json();

    // 缓存数据
    mockDataCache.set(cacheKey, mockData);

    Logger.log(`Loaded mock data from ${mockDataPath}`);
    return mockData;
  } catch (error) {
    Logger.warn(
      `Failed to load mock data from ${mockDataPath}, using default data`
    );

    // 返回默认数据
    return getDefaultMockData(apiPath, method, requestData);
  }
}

/**
 * 确定模拟数据文件路径
 * @param {string} apiPath - API路径
 * @returns {string}
 */
function determineMockDataPath(apiPath) {
  // 解析API路径，提取模块和操作
  const pathParts = apiPath.split("/").filter(Boolean);

  if (pathParts.length >= 3) {
    const module = pathParts.slice(0, -1).join("-");
    const action = pathParts[pathParts.length - 1];

    return `/mock/${module}-${action}.json`;
  }

  // 默认路径
  return `/mock/default-${pathParts[pathParts.length - 1] || "data"}.json`;
}

/**
 * 获取默认模拟数据
 * @param {string} apiPath - API路径
 * @param {string} method - HTTP方法
 * @param {object} requestData - 请求数据
 * @returns {any}
 */
function getDefaultMockData(apiPath, method, requestData) {
  // 根据API路径和方法生成默认数据
  if (apiPath.includes("queryPage") || apiPath.includes("list")) {
    return generateDefaultListData(requestData);
  }

  if (apiPath.includes("getDropLists")) {
    return generateDefaultDropListData();
  }

  if (
    method === "POST" &&
    (apiPath.includes("add") ||
      apiPath.includes("edit") ||
      apiPath.includes("update"))
  ) {
    return { message: "操作成功" };
  }

  if (method === "POST" && apiPath.includes("delete")) {
    return { message: "删除成功" };
  }

  if (apiPath.includes("export")) {
    return { message: "导出成功", data: "mock-file-url" };
  }

  if (apiPath.includes("import")) {
    return { message: "导入成功", data: "导入成功，共处理 0 条数据" };
  }

  return { message: "success", data: null };
}

/**
 * 生成默认列表数据
 * @param {object} requestData - 请求数据
 * @returns {object}
 */
function generateDefaultListData(requestData) {
  const pageNum = requestData.pageNum || 1;
  const pageSize = requestData.pageSize || 10;
  const total = 50; // 默认总数

  const data = [];
  for (let i = 0; i < Math.min(pageSize, total); i++) {
    data.push({
      id: (pageNum - 1) * pageSize + i + 1,
      name: `模拟数据 ${(pageNum - 1) * pageSize + i + 1}`,
      status: Math.random() > 0.5 ? "1" : "0",
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
    });
  }

  return { data, total, pageNum, pageSize };
}

/**
 * 生成默认下拉列表数据
 * @returns {object}
 */
function generateDefaultDropListData() {
  return {
    status: ["启用", "停用"],
    type: ["类型1", "类型2", "类型3"],
    category: ["分类A", "分类B", "分类C"],
  };
}

/**
 * 生成模拟响应
 * @param {any} mockData - 模拟数据
 * @param {string} apiPath - API路径
 * @param {object} requestData - 请求数据
 * @returns {object}
 */
function generateMockResponse(mockData, apiPath, requestData) {
  // 如果Mock数据已经包含完整的响应格式，直接返回
  if (
    mockData &&
    typeof mockData === "object" &&
    mockData.hasOwnProperty("success") &&
    mockData.hasOwnProperty("code")
  ) {
    return mockData;
  }

  // 如果是分页查询
  if (apiPath.includes("queryPage") || apiPath.includes("list")) {
    return generatePaginationResponse(
      mockData.data || mockData,
      requestData.pageNum || 1,
      requestData.pageSize || 10,
      mockData.total || (mockData.data ? mockData.data.length : 0)
    );
  }

  // 标准响应
  return generateStandardResponse(
    mockData.data || mockData,
    true,
    mockData.message || "success"
  );
}

/**
 * 生成标准响应格式
 * @param {any} data - 响应数据
 * @param {boolean} isSuccess - 是否成功
 * @param {string} message - 响应消息
 * @returns {object}
 */
function generateStandardResponse(data, isSuccess = true, message = "success") {
  return {
    success: isSuccess,
    code: isSuccess ? "10000" : "50000",
    message,
    data,
    traceId:
      "mock-" +
      Date.now() +
      "-" +
      Math.random()
        .toString(36)
        .substr(2, 9),
  };
}

/**
 * 生成分页响应格式
 * @param {array} data - 数据数组
 * @param {number} pageNum - 页码
 * @param {number} pageSize - 页大小
 * @param {number} total - 总数
 * @returns {object}
 */
function generatePaginationResponse(
  data,
  pageNum = 1,
  pageSize = 10,
  total = 0
) {
  return {
    success: true,
    code: "10000",
    message: "success",
    data: Array.isArray(data) ? data : [],
    pageNum: parseInt(pageNum),
    pageSize: parseInt(pageSize),
    total: parseInt(total),
    traceId:
      "mock-" +
      Date.now() +
      "-" +
      Math.random()
        .toString(36)
        .substr(2, 9),
  };
}

/**
 * 生成错误响应
 * @param {string} message - 错误消息
 * @returns {object}
 */
function generateErrorResponse(message = "Internal Server Error") {
  return {
    success: false,
    code: "50000",
    message,
    data: null,
    traceId:
      "mock-" +
      Date.now() +
      "-" +
      Math.random()
        .toString(36)
        .substr(2, 9),
  };
}

/**
 * 检查是否为下载接口
 * @param {string} apiPath - API路径
 * @returns {boolean}
 */
function isDownloadApi(apiPath) {
  const downloadApis = mockConfig?.special?.downloadApis || [];
  return (
    downloadApis.some((api) => apiPath.includes(api)) ||
    apiPath.includes("export")
  );
}

/**
 * 检查是否为上传接口
 * @param {string} apiPath - API路径
 * @returns {boolean}
 */
function isUploadApi(apiPath) {
  const uploadApis = mockConfig?.special?.uploadApis || [];
  return (
    uploadApis.some((api) => apiPath.includes(api)) ||
    apiPath.includes("import")
  );
}

/**
 * 处理下载响应
 * @param {any} mockData - 模拟数据
 * @returns {Response}
 */
function handleDownloadResponse(mockData) {
  // 模拟Excel文件下载
  const excelContent = "mock excel content";
  const blob = new Blob([excelContent], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  return new Response(blob, {
    status: 200,
    statusText: "OK",
    headers: {
      "Content-Type":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": 'attachment; filename="mock-export.xlsx"',
    },
  });
}

/**
 * 处理上传响应
 * @param {any} mockData - 模拟数据
 * @returns {Response}
 */
function handleUploadResponse(mockData) {
  const response = generateStandardResponse(
    mockData.data || "导入成功，共处理 0 条数据",
    true,
    mockData.message || "导入成功"
  );

  return new Response(JSON.stringify(response), {
    status: 200,
    statusText: "OK",
    headers: {
      "Content-Type": "application/json;charset=utf-8",
    },
  });
}
